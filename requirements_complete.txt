certifi==2025.7.14
charset-normalizer==3.4.2
colorama==0.4.6
contourpy==1.3.2
cycler==0.12.1
et-xmlfile==1.1.0
filelock==3.13.1
fonttools==4.59.0
fsspec==2024.6.1
idna==3.10
Jinja2==3.1.4
joblib @ file:///C:/b/abs_f4b98l6lgk/croot/joblib_1718217224240/work
kiwisolver==1.4.8
MarkupSafe==2.1.5
matplotlib==3.10.3
mkl-service==2.4.0
mkl_fft @ file:///C:/Users/<USER>/mkl/mkl_fft_1730823082242/work
mkl_random @ file:///C:/Users/<USER>/mkl/mkl_random_1730822522280/work
mpmath==1.3.0
networkx==3.3
numpy @ file:///C:/b/abs_0123vcxhf8/croot/numpy_and_numpy_base_1725470331966/work/dist/numpy-2.0.1-cp310-cp310-win_amd64.whl#sha256=e824ec3b279f4c5207736c83ac2f2dc79b489e08501a4ea9e3fc178c45db289b
opencv-python==*********
openpyxl @ file:///C:/b/abs_6cdczthntz/croot/openpyxl_1736371239628/work
packaging==25.0
pandas==2.3.1
pillow==11.0.0
psutil==7.0.0
py-cpuinfo==9.0.0
pyparsing==3.2.3
python-dateutil==2.9.0.post0
pytz==2025.2
PyYAML==6.0.2
requests==2.32.4
scikit-learn @ file:///C:/b/abs_45qemhn4lg/croot/scikit-learn_1753427401078/work
scipy @ file:///C:/b/abs_71q96u23a4/croot/scipy_1747238057723/work/dist/scipy-1.15.3-cp310-cp310-win_amd64.whl#sha256=06013d45c5cad262bccdca1fbe5c25a720be04ae66dc83d75d1f749d118a7346
seaborn==0.13.2
six==1.17.0
sympy==1.13.3
threadpoolctl @ file:///C:/b/abs_def0dwqlft/croot/threadpoolctl_1719407816649/work
torch==2.7.1+cu118
torchaudio==2.7.1+cu118
torchvision==0.22.1+cu118
tqdm==4.67.1
typing_extensions==4.12.2
tzdata==2025.2
ultralytics==8.3.170
ultralytics-thop==2.0.14
urllib3==2.5.0
