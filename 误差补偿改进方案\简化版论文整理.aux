\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\@writefile{toc}{\contentsline {section}{\numberline {1}引言}{1}{section.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {2}数学理论基础}{1}{section.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.1}机器人运动学模型}{1}{subsection.2.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.2}误差建模}{1}{subsection.2.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.3}局部最优问题分析}{2}{subsection.2.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces 损失函数地形图对比。(a)传统损失函数存在多个局部最优点（红色圆点），优化容易陷入局部解；(b)PINN通过物理约束平滑化损失函数，有效避免局部最优陷阱，更容易收敛到全局最优点（金色星号）。}}{2}{figure.1}\protected@file@percent }
\newlabel{fig:loss_landscape}{{1}{2}{损失函数地形图对比。(a)传统损失函数存在多个局部最优点（红色圆点），优化容易陷入局部解；(b)PINN通过物理约束平滑化损失函数，有效避免局部最优陷阱，更容易收敛到全局最优点（金色星号）。}{figure.1}{}}
\@writefile{toc}{\contentsline {section}{\numberline {3}增强物理信息神经网络设计}{2}{section.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.1}注意力增强架构}{2}{subsection.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.2}自适应多目标损失函数}{3}{subsection.3.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.3}增强物理约束设计}{3}{subsection.3.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces Transformer注意力机制学习关节耦合关系。(a)基于运动学理论的关节耦合矩阵，颜色深度和数值标注表示耦合强度；(b)神经网络学习到的注意力权重矩阵，与理论矩阵高度一致；(c)关节对耦合强度对比，蓝色柱为理论耦合，橙色柱为学习注意力，验证了注意力机制能够有效学习物理系统的内在结构。}}{4}{figure.2}\protected@file@percent }
\newlabel{fig:attention_mechanism}{{2}{4}{Transformer注意力机制学习关节耦合关系。(a)基于运动学理论的关节耦合矩阵，颜色深度和数值标注表示耦合强度；(b)神经网络学习到的注意力权重矩阵，与理论矩阵高度一致；(c)关节对耦合强度对比，蓝色柱为理论耦合，橙色柱为学习注意力，验证了注意力机制能够有效学习物理系统的内在结构。}{figure.2}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces 物理约束对神经网络预测的影响。(a)运动学约束确保预测符合机器人运动规律，橙色散点为无约束预测，蓝色实线为有约束预测；(b)能量守恒约束维持系统物理一致性，橙色虚线违反守恒定律，蓝色实线符合守恒；(c)关节限制约束避免超出物理限制的预测，灰色虚线为关节限制边界；(d)适当的物理约束权重λ显著改善收敛稳定性。}}{5}{figure.3}\protected@file@percent }
\newlabel{fig:physics_constraints}{{3}{5}{物理约束对神经网络预测的影响。(a)运动学约束确保预测符合机器人运动规律，橙色散点为无约束预测，蓝色实线为有约束预测；(b)能量守恒约束维持系统物理一致性，橙色虚线违反守恒定律，蓝色实线符合守恒；(c)关节限制约束避免超出物理限制的预测，灰色虚线为关节限制边界；(d)适当的物理约束权重λ显著改善收敛稳定性。}{figure.3}{}}
\@writefile{toc}{\contentsline {section}{\numberline {4}增强PINN训练算法}{6}{section.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1}多重学习率调度策略}{6}{subsection.4.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2}早停与数值稳定性策略}{7}{subsection.4.2}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {1}{\ignorespaces 增强PINN训练算法}}{7}{algorithm.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {5}多目标优化算法}{8}{section.5}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.1}改进的NSGA-II算法}{8}{subsection.5.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.2}非支配关系与拥挤距离}{8}{subsection.5.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.3}Pareto最优解选择}{8}{subsection.5.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6}确定性初始化策略}{8}{section.6}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.1}物理先验初始化}{8}{subsection.6.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces NSGA-II多目标优化结果。(a)二维Pareto前沿展示位置精度与角度精度的权衡关系，深绿色点为所有候选解，橙色点为Pareto最优解，蓝色虚线连接前沿轨迹；(b)三维目标空间显示精度与复杂度的平衡关系；(c)收敛历史展示最佳位置误差（蓝线）和角度误差（紫线）随进化代数的变化，验证了算法的有效性。}}{9}{figure.4}\protected@file@percent }
\newlabel{fig:multi_objective}{{4}{9}{NSGA-II多目标优化结果。(a)二维Pareto前沿展示位置精度与角度精度的权衡关系，深绿色点为所有候选解，橙色点为Pareto最优解，蓝色虚线连接前沿轨迹；(b)三维目标空间显示精度与复杂度的平衡关系；(c)收敛历史展示最佳位置误差（蓝线）和角度误差（紫线）随进化代数的变化，验证了算法的有效性。}{figure.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.2}自适应权重调整}{9}{subsection.6.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {7}智能物理驱动特征工程}{9}{section.7}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {7.1}85维优化特征构造}{9}{subsection.7.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces 确定性初始化与随机初始化对比。(a)随机初始化收敛曲线显示较大的方差和不稳定性；(b)确定性初始化收敛曲线更加稳定一致；(c)最终收敛性能对比显示确定性方法的优势；(d)收敛速度分布表明确定性方法平均收敛轮数更少。}}{10}{figure.5}\protected@file@percent }
\newlabel{fig:deterministic_comparison}{{5}{10}{确定性初始化与随机初始化对比。(a)随机初始化收敛曲线显示较大的方差和不稳定性；(b)确定性初始化收敛曲线更加稳定一致；(c)最终收敛性能对比显示确定性方法的优势；(d)收敛速度分布表明确定性方法平均收敛轮数更少。}{figure.5}{}}
\@writefile{toc}{\contentsline {section}{\numberline {8}实验验证}{11}{section.8}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {8.1}实验设置}{11}{subsection.8.1}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {1}{\ignorespaces 方法性能对比}}{12}{table.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {8.2}性能对比}{12}{subsection.8.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {8.3}主要结果}{12}{subsection.8.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {9}技术创新与贡献}{12}{section.9}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.1}关键技术创新}{12}{subsection.9.1}\protected@file@percent }
\bibcite{raissi2019physics}{1}
\bibcite{attention2017}{2}
\bibcite{resnet2016}{3}
\bibcite{robotics_survey_2024}{4}
\bibcite{qiao2019svr}{5}
\bibcite{adamw2017}{6}
\bibcite{gelu2016}{7}
\@writefile{toc}{\contentsline {subsection}{\numberline {9.2}实验验证优势}{13}{subsection.9.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {10}结论}{13}{section.10}\protected@file@percent }
\bibcite{early_stopping}{8}
\bibcite{lagrangian_robotics}{9}
\gdef \@abspage@last{14}
